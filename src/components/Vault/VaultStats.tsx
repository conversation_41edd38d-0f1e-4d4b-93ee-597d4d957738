"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { UserDocument } from '@/types/database.types';
import { formatFileSize } from '@/lib/utils';

interface VaultStatsProps {
  documents: UserDocument[];
}

export default function VaultStats({ documents }: VaultStatsProps) {
  const totalSize = documents.reduce((sum, doc) => sum + (doc.file_size || 0), 0);
  const storageLimit = 5 * 1024 * 1024 * 1024; // 5GB
  const usagePercent = Math.min(100, (totalSize / storageLimit) * 100);

  const categoryCounts = documents.reduce((acc, doc) => {
    const category = doc.category || 'other';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold mb-4">Vault Overview</h3>
        
        <div className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Storage Used</span>
              <span>{formatFileSize(totalSize)} / {formatFileSize(storageLimit)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div 
                className="bg-blue-600 h-2.5 rounded-full" 
                style={{ width: `${usagePercent}%` }}
              ></div>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Documents by Category</h4>
            <div className="space-y-2">
              {Object.entries(categoryCounts).map(([category, count]) => (
                <div key={category} className="flex justify-between text-sm">
                  <span className="capitalize">{category}</span>
                  <span>{count}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
