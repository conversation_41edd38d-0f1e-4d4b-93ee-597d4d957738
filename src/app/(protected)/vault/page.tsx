"use client";

import React, { useEffect, useState, useCallback } from 'react';
import Link from 'next/link';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/auth-context';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Search, FileText, Download, Eye, ArrowLeft, Trash2 } from 'lucide-react';
import PageHeading from '@/components/ui/PageHeading';
import { toast } from 'sonner';
import { UserDocument } from '@/types/database.types';
import UploadDocumentDialog from '@/components/Vault/UploadDocumentDialog';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import VaultStats from '@/components/Vault/VaultStats';

const documentCategories = [
  { value: 'all', label: 'All Documents' },
  { value: 'financial', label: 'Financial Records' },
  { value: 'legal', label: 'Legal Documents' },
  { value: 'personal', label: 'Personal Documents' },
  { value: 'insurance', label: 'Insurance' },
  { value: 'other', label: 'Other' },
];

export default function VaultPage() {
  const { user } = useAuth();
  const [documents, setDocuments] = useState<UserDocument[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<UserDocument[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<UserDocument | null>(null);

  const fetchDocuments = useCallback(async () => {
    try {
      setIsLoading(true);

      let url = '/api/vault/documents';
      const params = new URLSearchParams();

      if (searchQuery) {
        params.append('search', searchQuery);
      }

      if (selectedCategory && selectedCategory !== 'all') {
        params.append('category', selectedCategory);
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url);
      if (!response.ok) throw new Error(`Failed to fetch documents: ${response.statusText}`);

      const data = await response.json();
      setDocuments(data || []);
      setFilteredDocuments(data || []);
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast.error(`Failed to load documents: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setDocuments([]);
      setFilteredDocuments([]);
    } finally {
      setIsLoading(false);
    }
  }, [searchQuery, selectedCategory]);

  useEffect(() => {
    if (user) fetchDocuments();
  }, [user, fetchDocuments]);

  const handleViewDocument = async (document: UserDocument) => {
    try {
      setIsProcessing(document.id);
      const response = await fetch(`/api/documents/${document.id}/download`);
      
      if (!response.ok) {
        throw new Error(`Failed to download document: ${response.statusText}`);
      }

      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      window.open(blobUrl, '_blank');
      URL.revokeObjectURL(blobUrl); // Clean up memory
    } catch (error) {
      console.error('Error viewing document:', error);
      toast.error(`Failed to view document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(null);
    }
  };

  const handleDownloadDocument = async (document: UserDocument) => {
    try {
      setIsProcessing(document.id);
      const response = await fetch(`/api/documents/${document.id}/download`);
      
      if (!response.ok) {
        throw new Error(`Failed to download document: ${response.statusText}`);
      }

      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      const a = window.document.createElement('a');
      a.href = blobUrl;
      a.download = document.name;
      window.document.body.appendChild(a);
      a.click();
      window.document.body.removeChild(a);
      URL.revokeObjectURL(blobUrl); // Clean up memory
    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error(`Failed to download document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(null);
    }
  };

  const confirmDeleteDocument = (document: UserDocument) => {
    setSelectedDocument(document);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteDocument = async () => {
    if (!selectedDocument) return;

    try {
      setIsDeleting(selectedDocument.id);
      const response = await fetch(`/api/documents/${selectedDocument.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error(`Failed to delete document: ${response.statusText}`);

      toast.success('Document deleted successfully');
      setDocuments(documents.filter(doc => doc.id !== selectedDocument.id));
      setFilteredDocuments(filteredDocuments.filter(doc => doc.id !== selectedDocument.id));
      setIsDeleteDialogOpen(false);
      setSelectedDocument(null);
    } catch (error) {
      console.error('Error deleting document:', error);
      toast.error(`Failed to delete document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsDeleting(null);
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex justify-between items-center mb-6">
        <Button variant="outline" asChild>
          <Link href="/dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <PageHeading
        title="Digital Vault"
        description="Securely store and manage your important documents."
        actions={
          <div className="flex gap-2">
            {selectedCategory !== 'all' && (
              <Button variant="outline" onClick={() => setSelectedCategory('all')}>
                View All
              </Button>
            )}
            <UploadDocumentDialog onDocumentUploaded={fetchDocuments} />
          </div>
        }
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
        {/* Stats Panel */}
        <div className="lg:col-span-1">
          <VaultStats documents={documents} />
        </div>

        {/* Documents List */}
        <div className="lg:col-span-2">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">
                {selectedCategory === 'all' ? 'All Documents' : `${documentCategories.find(c => c.value === selectedCategory)?.label}`}
                <span className="ml-2 text-sm font-normal text-gray-500">
                  ({filteredDocuments.length} {filteredDocuments.length === 1 ? 'item' : 'items'})
                </span>
              </h3>

              {isLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <Skeleton className="h-16 w-16 rounded-md" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-1/3" />
                        <Skeleton className="h-4 w-1/4" />
                      </div>
                      <Skeleton className="h-8 w-16" />
                    </div>
                  ))}
                </div>
              ) : filteredDocuments.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-gray-500 mb-4">No documents found</p>
                  <UploadDocumentDialog onDocumentUploaded={fetchDocuments} />
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredDocuments.map((document) => (
                    <div
                      key={document.id}
                      className="flex items-center p-4 border rounded-lg hover:bg-gray-50 transition-colors h-24"
                    >
                      <div className="h-16 w-16 rounded-md bg-gray-100 mr-4 overflow-hidden flex-shrink-0 flex items-center justify-center">
                        <FileText className="h-8 w-8 text-gray-400" />
                      </div>

                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 truncate">{document.name}</h4>
                        <div className="flex items-center text-sm text-gray-500 mt-1">
                          <span className="bg-blue-50 text-blue-700 px-2 py-0.5 rounded text-xs">
                            {document.category || 'Other'}
                          </span>
                        </div>
                      </div>

                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleViewDocument(document)}
                          disabled={isProcessing === document.id}
                        >
                          {isProcessing === document.id ? (
                            <div className="animate-spin h-4 w-4 border-2 border-gray-400 rounded-full border-t-transparent" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDownloadDocument(document)}
                          disabled={isProcessing === document.id}
                        >
                          {isProcessing === document.id ? (
                            <div className="animate-spin h-4 w-4 border-2 border-gray-400 rounded-full border-t-transparent" />
                          ) : (
                            <Download className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => confirmDeleteDocument(document)}
                          disabled={isProcessing === document.id}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the document "{selectedDocument?.name}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteDocument} 
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting === selectedDocument?.id ? (
                <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent" />
              ) : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
